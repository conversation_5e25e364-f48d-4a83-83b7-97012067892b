import type { RetrievalWorkbookUpdate } from '../../workbookTypes';

import React from 'react';
import { RetrievalWorkbook } from '../../workbookTypes';
import { MdOutlineModeEdit } from 'react-icons/md';
import { FaPlus } from 'react-icons/fa';
import { FaCheck } from 'react-icons/fa6';
import { RxCross2 } from 'react-icons/rx';
import { DotLoader } from 'react-spinners';
import { useNavigate } from 'react-router-dom';

import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  updateWorkbookById,
  selectUserWorkbookPendingStatus,
  selectGlobalWorkbookPendingStatus,
  selectMockLoadingFiles,
  createMyWorkbookSession,
} from '@/store/slices/workbookSlice';
import { selectCurrentUser } from '@/store/slices/authSlice';
import { handleFileChangeWithAttestation } from '@/utils/ucdFileUploadWrapper';
import { buildWorkbookBasePath } from '../../utils/workbookUtils';

import WorkbookUtilityFile from './WorkbookUtilityFile';

import useThemeStyles from '@hooks/useThemeStyles';
import './styles.scss';

export type WorkbookUtilityBarProps = {
  isGlobal: boolean;
  workbook?: RetrievalWorkbook;
  onFileUpload?: (files: File[]) => void;
};

const WorkbookUtilityBar: React.FC<WorkbookUtilityBarProps> = (props: WorkbookUtilityBarProps) => {
  const { isGlobal, workbook, onFileUpload } = props;
  const { classes, colors: themeColors } = useThemeStyles();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const pendingStatus = isGlobal ? selectGlobalWorkbookPendingStatus : selectUserWorkbookPendingStatus;
  const workbookPendingStatus = useAppSelector(state => pendingStatus(state, workbook?.id ?? ''));
  const mockLoadingFiles = useAppSelector(state => selectMockLoadingFiles(state, workbook?.id ?? ''));
  const currentUser = useAppSelector(selectCurrentUser);
  const userIsAuthor = Boolean(workbook?.author) && workbook?.author === currentUser;

  const [editingWorkbookName, setEditingWorkbookName] = React.useState(false);
  const [updateWorkbookName, setUpdateWorkbookName] = React.useState(workbook?.name ?? '');

  const filesToRender = () => {
    const workbookFiles = (workbook?.files ?? []).slice();
    const mockFiles = mockLoadingFiles.slice();

    for (const mockFile of mockFiles) {
      if (!workbookFiles.find(f => f.name === mockFile.name)) {
        workbookFiles.push(mockFile);
      }
    }
    workbookFiles.sort((fileA, fileB) => fileA.name.localeCompare(fileB.name));
    return workbookFiles;
  };

  const onFilesChanged = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!onFileUpload) return;

    await handleFileChangeWithAttestation(event, 'workbook', currentUser, onFileUpload);
  };

  const onUpdateWorkbookNameChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    setUpdateWorkbookName(event.currentTarget.value);
  };

  const onToggleEditWorkbookName = async (isEditing: boolean) => {
    setUpdateWorkbookName(isEditing ? (workbook?.name ?? '') : '');
    setEditingWorkbookName(isEditing);
  };

  const onWorkbookNameSave = async () => {
    if (workbook && workbook.name !== updateWorkbookName && workbook.author == currentUser) {
      const lastUpdated = workbook.updatedUtc;
      const workbookUpdates: RetrievalWorkbookUpdate = {
        id: workbook.id,
        name: updateWorkbookName,
        clear_description: false,
      };
      const updateResult = await dispatch(updateWorkbookById({ workbookUpdates, isGlobal })).unwrap();
      if (updateResult.updatedUtc !== lastUpdated) {
        setEditingWorkbookName(false);
      }
    }
  };

  const handleCreateNewSession = async () => {
    if (workbook) {
      try {
        await dispatch(createMyWorkbookSession({ workbookId: workbook.id, isGlobal: isGlobal })).unwrap();
        // Navigate to workbook without sessionId to show the new session
        const workbookPath = buildWorkbookBasePath(workbook.id, isGlobal);
        navigate(workbookPath);
      } catch (error) {}
    }
  };

  const WorkbookName = (
    <>
      <div className={`workbook-name-text ${classes.text}`}>{workbook?.name ?? ''}</div>
      {userIsAuthor && (
        <div className="workbook-name-edit" onClick={() => onToggleEditWorkbookName(true)}>
          <MdOutlineModeEdit fill={'#ECF7FF'} size={16} />
        </div>
      )}
    </>
  );

  const WorkbookNameEdit = (
    <>
      <div className="workbook-name-edit-input bg-white border-2 border-[#0066B1] w-full p-2 rounded">
        <input
          onChange={onUpdateWorkbookNameChange}
          value={updateWorkbookName ?? ''}
          className="text-black w-full outline-none"
        ></input>
      </div>
      {(workbookPendingStatus?.isUpdating ?? false) ? (
        <DotLoader loading={true} color="#ECF7FF" size={16} />
      ) : (
        <>
          <div className="workbook-name-edit-save" onClick={() => onWorkbookNameSave()}>
            <FaCheck fill={'#ECF7FF'} size={16} />
          </div>
          <div className="workbook-name-edit-cancel" onClick={() => onToggleEditWorkbookName(false)}>
            <RxCross2 color="#F7987D" size={16} />
          </div>
        </>
      )}
    </>
  );

  return (
    <div className="workbook-utility-bar-main">
      <div className="workbook-name">{editingWorkbookName ? WorkbookNameEdit : WorkbookName}</div>
      <div className="workbook-description"></div>
      <div className="workbook-files">
        {filesToRender().map(file => {
          return (
            <WorkbookUtilityFile
              key={file.name}
              workbookId={workbook?.id}
              file={file}
              isGlobal={isGlobal}
              allowDelete={userIsAuthor}
            />
          );
        })}
      </div>
      <div className="workbook-actions">
        {userIsAuthor && (
          <div className="workbook-add-file">
            <label
              htmlFor="uploadWorkbookFiles"
              className={`workbook-add-file-label ${themeColors.text} hover:bg-[#0066B1] hover:cursor-pointer`}
            >
              <div className="workbook-add-file-icon">
                <FaPlus fill="white" />
              </div>
              <div className={`workbook-add-file-text ${classes.text} font-roboto`}>Upload files</div>
            </label>
            <input id="uploadWorkbookFiles" type="file" multiple hidden onChange={onFilesChanged} accept=".txt, .pdf" />
          </div>
        )}
        <div className="workbook-new-session hover:bg-[#0066B1] hover:cursor-pointer" onClick={handleCreateNewSession}>
          <div className={`workbook-new-session-text ${classes.text}`}>New chat session</div>
        </div>
      </div>
    </div>
  );
};

export default WorkbookUtilityBar;
