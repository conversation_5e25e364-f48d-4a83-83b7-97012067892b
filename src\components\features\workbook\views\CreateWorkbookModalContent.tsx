import type { ChangeEvent } from 'react';
import { RetrievalWorkbookChunkSize } from '../workbookTypes';

import { useState, useCallback, useEffect } from 'react';

import { Radio } from '@base-ui-components/react/radio';
import { RadioGroup } from '@base-ui-components/react/radio-group';

import { FaPlus } from 'react-icons/fa';
import { RxCross2 } from 'react-icons/rx';
import { MdOutlineFilePresent, MdOutlineEdit } from 'react-icons/md';
import { TbNotebook } from 'react-icons/tb';
import { RiInformationFill } from 'react-icons/ri';
import { IoClose } from 'react-icons/io5';
import { AiOutlineLoading3Quarters } from 'react-icons/ai';

import { WORKBOOK_CONSTANTS } from '@/config/constants';
import { withUCDAttestation } from '@/utils/ucdFileUploadWrapper';
import { useAppSelector } from '@/store/hooks';
import { selectCurrentUser } from '@/store/slices/authSlice';

import './CreateWorkbookModalContent.scss';

interface CreateWorkbookModalProps {
  isGlobal: boolean;
  isOpen: boolean;
  onSubmit: (
    workbookName: string,
    systemInstructions: string,
    chunkSize: RetrievalWorkbookChunkSize,
    files: File[]
  ) => Promise<void>;
  onClose: () => void;
}

interface ChunkSizeRadioGroupProps {
  selectedChunkSize: RetrievalWorkbookChunkSize;
  onChunkSizeSelect: (chunkSize: RetrievalWorkbookChunkSize) => void;
}

const ChunkSizeRadioGroup = ({ selectedChunkSize, onChunkSizeSelect }: ChunkSizeRadioGroupProps) => {
  const handleChunkSizeChange = useCallback(
    (value: unknown) => {
      onChunkSizeSelect(value as RetrievalWorkbookChunkSize);
    },
    [onChunkSizeSelect]
  );

  return (
    <RadioGroup value={selectedChunkSize} className="chunk-size-radio-group" onValueChange={handleChunkSizeChange}>
      <label className="chunk-size-radio-group__option">
        <Radio.Root value={RetrievalWorkbookChunkSize.Small} className="radio-item">
          <Radio.Indicator className="radio-indicator" />
        </Radio.Root>
        Small
      </label>
      <label className="chunk-size-radio-group__option">
        <Radio.Root value={RetrievalWorkbookChunkSize.Medium} className="radio-item">
          <Radio.Indicator className="radio-indicator" />
        </Radio.Root>
        Medium
      </label>
      <label className="chunk-size-radio-group__option">
        <Radio.Root value={RetrievalWorkbookChunkSize.Large} className="radio-item">
          <Radio.Indicator className="radio-indicator" />
        </Radio.Root>
        Large (recommended)
      </label>
    </RadioGroup>
  );
};

const CreateWorkbookModal = ({ isGlobal, isOpen, onSubmit, onClose }: CreateWorkbookModalProps) => {
  const currentUser = useAppSelector(selectCurrentUser);

  const [workbookName, setWorkbookName] = useState('');
  const [systemInstructions, setSystemInstructions] = useState('');
  const [workbookChunkSize, setWorkbookChunkSize] = useState(RetrievalWorkbookChunkSize.Large);
  const [filesToUpload, setFilesToUpload] = useState<File[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Reset all states when modal opens
  useEffect(() => {
    if (isOpen) {
      setWorkbookName('');
      setSystemInstructions('');
      setWorkbookChunkSize(RetrievalWorkbookChunkSize.Large);
      setFilesToUpload([]);
      setIsCreating(false);
      setErrorMessage('');
    }
  }, [isOpen]);

  const isSubmitDisabled = workbookName.trim().length === 0 || isCreating;

  const handleFormSubmit = useCallback(async () => {
    if (!isSubmitDisabled) {
      setIsCreating(true);
      setErrorMessage(''); // Clear any previous errors
      try {
        await onSubmit(workbookName.trim(), systemInstructions.trim(), workbookChunkSize, filesToUpload);
      } catch (error) {
        console.error('Failed to create workbook:', error);
        if (error instanceof Error) {
          setErrorMessage(error.message);
        } else {
          setErrorMessage('Failed to create workbook. Please try again.');
        }
      } finally {
        setIsCreating(false);
      }
    }
  }, [isSubmitDisabled, workbookName, systemInstructions, workbookChunkSize, filesToUpload, onSubmit]);

  const handleNameChange = useCallback((event: ChangeEvent<HTMLInputElement>) => {
    setWorkbookName(event.target.value);
    // Clear error when user starts typing
    if (errorMessage) {
      setErrorMessage('');
    }
  }, [errorMessage]);

  const handleSystemInstructionsChange = useCallback((event: ChangeEvent<HTMLTextAreaElement>) => {
    setSystemInstructions(event.target.value);
    // Clear error when user modifies form
    if (errorMessage) {
      setErrorMessage('');
    }
  }, [errorMessage]);

  const validateAndProcessFiles = useCallback((validatedFiles: File[]) => {
    const { fileSizeLimits } = WORKBOOK_CONSTANTS;
    const validFiles: File[] = [];
    const errors: string[] = [];

    validatedFiles.forEach(file => {
      const sizeLimit = fileSizeLimits[file.type as keyof typeof fileSizeLimits];

      if (!sizeLimit) {
        errors.push(`Unsupported file type: ${file.type}`);
        return;
      }

      if (file.size > sizeLimit) {
        const maxSizeMB = file.type === 'text/plain' ? '10 MB' : '200 MB';
        errors.push(`${file.name} exceeds the ${maxSizeMB} size limit`);
        return;
      }

      validFiles.push(file);
    });

    // Set error message if there are file validation errors
    if (errors.length > 0) {
      setErrorMessage(errors[0]); // Show first error
    } else {
      setErrorMessage(''); // Clear error if files are valid
    }

    setFilesToUpload(prevFiles => [...prevFiles, ...validFiles]);
  }, []);

  const handleFilesChange = useCallback(
    async (event: ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(event.target.files || []);
      if (files.length === 0) return;

      await withUCDAttestation(files, 'workbook', currentUser, validateAndProcessFiles, files);
      // Reset input value to allow selecting the same files again
      event.target.value = '';
    },
    [currentUser, validateAndProcessFiles]
  );

  const handleFileDelete = useCallback((fileToDelete: File) => {
    setFilesToUpload(prevFiles => prevFiles.filter(file => file !== fileToDelete));
  }, []);

  const handleOverlayClick = useCallback(
    (event: React.MouseEvent) => {
      if (event.target === event.currentTarget) {
        onClose();
      }
    },
    [onClose]
  );

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    },
    [onClose]
  );

  if (!isOpen) return null;

  return (
    <div className="create-workbook-modal-overlay" onClick={handleOverlayClick} onKeyDown={handleKeyDown} tabIndex={-1}>
      <div className="create-workbook">
        {/* Close Button */}
        <button className="create-workbook__close-btn" onClick={onClose} aria-label="Close modal">
          <IoClose size={24} />
        </button>

        <div className="create-workbook__content custom-scrollbar-minimal">
          {/* Header */}
          <h1 className="create-workbook__title">New {isGlobal ? 'Public ' : ''}Workbook</h1>

          {/* Name Input */}
          <div className="input-group">
            <input
              className="input-field input-field--with-icon"
              value={workbookName}
              placeholder="Enter your workbook name here..."
              onChange={handleNameChange}
            />
            <MdOutlineEdit
              className={`input-field__icon ${workbookName.trim() ? 'input-field__icon--active' : 'input-field__icon--inactive'}`}
              size={24}
            />
          </div>

          {/* System Instructions */}
          <section className="system-instructions">
            <h3 className="section-title">System Instructions (optional)</h3>
            <p className="section-description">
              System instructions in Gemini models guide the model's behavior by providing additional context and
              constraints beyond the user's prompt. They can be used to define a persona, output format, style, tone,
              and goals, influencing the model's responses.
            </p>
            <div className="textarea-group">
              <textarea
                className="textarea-field textarea-field--with-icon"
                value={systemInstructions}
                placeholder="Enter your system instructions here..."
                onChange={handleSystemInstructionsChange}
              />
              <MdOutlineEdit
                className={`textarea-field__icon ${systemInstructions.trim() ? 'textarea-field__icon--active' : 'textarea-field__icon--inactive'}`}
                size={24}
              />
            </div>
          </section>

          {/* Chunk Size */}
          <section className="chunk-size-section">
            <h3 className="section-title">Document chunk size</h3>
            <ChunkSizeRadioGroup selectedChunkSize={workbookChunkSize} onChunkSizeSelect={setWorkbookChunkSize} />
          </section>

          {/* File Upload */}
          <section className="file-upload-section">
            <div className="add-files-main">
              <div className="add-files-button">
                <label htmlFor="workbook-files" className="add-files-label">
                  <FaPlus size={16} />
                  <span className="add-files-label-text">Add files</span>
                </label>
                <input id="workbook-files" type="file" multiple hidden onChange={handleFilesChange} accept=".txt, .pdf" />
              </div>

              {filesToUpload.length > 0 && (
                <div className="add-files-file-list custom-scrollbar-minimal">
                  {filesToUpload.map((file, index) => (
                    <div key={`${file.name}-${index}`} className="add-files-file">
                      <div className="add-files-file-icon">
                        <MdOutlineFilePresent size={16} color="#F7987D" />
                      </div>
                      <div className="add-files-file-name">
                        {file.name}
                      </div>
                      <div className="add-files-file-delete">
                        <RxCross2 
                          size={16} 
                          color="#FFFFFF" 
                          onClick={() => handleFileDelete(file)}
                          style={{ cursor: 'pointer' }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </section>

          {/* Actions */}
          <div className="form-actions">
            <button type="button" className="btn btn--secondary" onClick={onClose}>
              Cancel
            </button>
            <button
              type="button"
              className={`btn btn--primary ${isSubmitDisabled ? 'btn--disabled' : ''}`}
              disabled={isSubmitDisabled}
              onClick={handleFormSubmit}
            >
              {isCreating ? (
                <>
                  <AiOutlineLoading3Quarters className="animate-spin" size={24} />
                  <span>Creating...</span>
                </>
              ) : (
                <>
                  <TbNotebook size={24} />
                  <span>Create Notebook</span>
                </>
              )}
            </button>
          </div>
        </div>

        {/* Footer */}
        <footer className={`create-workbook__footer ${errorMessage ? 'create-workbook__footer--error' : ''}`}>
          <RiInformationFill size={20} />
          {errorMessage ? (
            <span>{errorMessage}</span>
          ) : (
            <span>Text files cannot exceed 10 MB in size, PDF files cannot exceed 200 MB in size.</span>
          )}
        </footer>
      </div>
    </div>
  );
};

export default CreateWorkbookModal;
