.edit-workbook-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.edit-workbook-modal {
  width: var(--lg-max-width, 800px);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  border-radius: var(--border-radius-border-radius-xxxl);
  background: var(--brand-hmk-primary-500);
  color: var(--text-text-invert);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.edit-workbook-modal__header {
  padding: var(--spacing-spacing-l-1) var(--spacing-spacing-l-3) var(--spacing-spacing-m-2) var(--spacing-spacing-l-3);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
}

.edit-workbook-modal__title {
  font-size: var(--spacing-spacing-m-3);
  font-weight: 600;
  margin: 0;
  color: var(--text-text-invert);
}

.edit-workbook-modal__content {
  padding: 0 var(--spacing-spacing-l-3) var(--spacing-spacing-l-1) var(--spacing-spacing-l-3);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  gap: var(--spacing-spacing-l-1);
}

.edit-workbook-modal__input-container {
  position: relative;
  width: 100%;
}

.edit-workbook-modal__input {
  width: 100%;
  padding: var(--spacing-spacing-m-1) var(--spacing-spacing-l-3) var(--spacing-spacing-m-1) var(--spacing-spacing-m-2);
  border: var(--border-weight-border-weight-s) solid var(--border-color-border-interactive);
  border-radius: var(--border-radius-border-radius-l);
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-text-invert);
  font-size: var(--spacing-spacing-m-2);
  outline: none;
  transition: border-color 0.2s ease;

  &:focus {
    border-color: var(--border-color-border-highlight);
  }

  &::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }
}

.edit-workbook-modal__input-icon {
  position: absolute;
  right: var(--spacing-spacing-m-1);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-text-invert);
  width: var(--spacing-spacing-m-3);
  height: var(--spacing-spacing-m-3);
}

.edit-workbook-modal__button-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.edit-workbook-modal__button {
  padding: var(--spacing-spacing-m-1) var(--spacing-spacing-m-3);
  border-radius: var(--border-radius-border-radius-l);
  font-size: var(--spacing-spacing-m-2);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.edit-workbook-modal__cancel-button {
  background: transparent;
  color: var(--text-text-invert);
  border: var(--border-weight-border-weight-s) solid rgba(255, 255, 255, 0.3);

  &:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.1);
  }
}

.edit-workbook-modal__save-button {
  background: var(--button-button-primary);
  color: var(--text-text-invert);

  &:hover:not(:disabled) {
    background: var(--border-color-border-highlight);
  }
}


